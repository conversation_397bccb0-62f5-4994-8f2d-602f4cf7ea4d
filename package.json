{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "db:migrate": "node scripts/run-migrations.js", "update-topics": "node scripts/update-topic-access.js", "update-topics-simple": "node scripts/update-topics-simple.js", "update-access": "node scripts/update-access-rules.js", "test:email": "node scripts/test-email-delivery.js", "test:auth-email": "node scripts/test-auth-email.js", "test:resend": "node scripts/test-resend-email.js", "apply:smtp": "node scripts/apply-smtp-config.js", "preview:emails": "node scripts/preview-email-templates.js", "restart:supabase": "node scripts/restart-supabase.js", "setup:env": "node setup-env.js", "setup:local": "bash setup-secquiz-local.sh", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:status": "supabase status", "supabase:reset": "supabase db reset", "dev:local": "supabase start && npm run dev", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "security:check": "npm audit", "security:check:full": "npm audit && npx snyk test", "security:fix": "npm audit fix", "security:fix:force": "npm audit fix --force", "security:wizard": "npx snyk wizard", "security:monitor": "npx snyk monitor", "security:setup": "node scripts/setup-snyk.js", "pwa:generate": "node scripts/generate-icons.js && node scripts/generate-screenshots.js", "prepare": "husky install"}, "dependencies": {"@capacitor/android": "^7.4.3", "@capacitor/cli": "^7.4.3", "@capacitor/core": "^7.4.3", "@chakra-ui/react": "^3.26.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.57.0", "@tanstack/react-query": "^5.56.2", "@types/express": "^5.0.3", "@types/react-icons": "^2.2.7", "@vercel/analytics": "^1.5.0", "@vitejs/plugin-react": "^4.7.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "commander": "^13.1.0", "csv-parse": "^5.6.0", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.23.12", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "open": "^10.1.1", "papaparse": "^5.5.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-paystack": "^6.0.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.30.1", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^22.5.5", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "canvas": "^3.1.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-security": "^3.0.1", "globals": "^15.9.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "postcss": "^8.4.47", "snyk": "^1.1296.2", "supabase": "^2.39.2", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^7.1.4", "vitest": "^3.1.1"}}