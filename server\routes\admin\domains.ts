import { Router, Request, Response } from 'express';
import { supabase } from '../../utils/supabaseClient.js';

const router = Router();

// Get all domains with stats
router.get('/', async (req: Request, res: Response) => {
  try {
    const { data, error } = await supabase
      .from('domains')
      .select(`
        *,
        domain_learning_paths(count),
        domain_subscription_plans(count),
        user_domain_progress(count)
      `);

    if (error) throw error;
    res.json(data);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
});

// Toggle domain status
router.patch('/:id/toggle', async (req: Request, res: Response) => {
  const { id } = req.params;
  const { is_active } = req.body;

  try {
    const { data, error } = await supabase.rpc('toggle_domain_status', {
      domain_uuid: id,
      new_status: is_active
    });

    if (error) throw error;
    res.json({ success: true, is_active });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
});

// Get domain statistics
router.get('/:id/stats', async (req: Request, res: Response) => {
  const { id } = req.params;

  try {
    const { data, error } = await supabase
      .from('user_domain_progress')
      .select(`
        *,
        domains!inner(*),
        user_profiles!inner(*)
      `)
      .eq('domain_id', id);

    if (error) throw error;

    const stats = {
      total_users: data.length,
      completed_users: data.filter((d: any) => d.completed_at).length,
      average_completion: data.reduce((acc: number, curr: any) => 
        acc + Number(curr.completion_percentage), 0) / (data.length || 1)
    };

    res.json(stats);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
});

export default router;