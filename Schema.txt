Below is a complete list of every table in your project, grouped by schema, along with each table’s columns (name and data type). Primary key columns are highlighted.

#Auth Schema
users

instance_id uuid
id uuid (PK)
aud character varying
role character varying
email character varying
encrypted_password character varying
email_confirmed_at timestamp with time zone
invited_at timestamp with time zone
confirmation_token character varying
confirmation_sent_at timestamp with time zone
recovery_token character varying
recovery_sent_at timestamp with time zone
email_change_token_new character varying
email_change character varying
email_change_sent_at timestamp with time zone
last_sign_in_at timestamp with time zone
raw_app_meta_data jsonb
raw_user_meta_data jsonb
is_super_admin boolean
created_at timestamp with time zone
updated_at timestamp with time zone
phone text (unique)
phone_confirmed_at timestamp with time zone
phone_change text
phone_change_token character varying
phone_change_sent_at timestamp with time zone
confirmed_at timestamp with time zone (generated)
email_change_token_current character varying
email_change_confirm_status smallint
banned_until timestamp with time zone
reauthentication_token character varying
reauthentication_sent_at timestamp with time zone
is_sso_user boolean
deleted_at timestamp with time zone
is_anonymous boolean
refresh_tokens

instance_id uuid
id bigint (PK)
token character varying (unique)
user_id character varying
revoked boolean
created_at timestamp with time zone
updated_at timestamp with time zone
parent character varying
session_id uuid
instances

id uuid (PK)
uuid uuid
raw_base_config text
created_at timestamp with time zone
updated_at timestamp with time zone
audit_log_entries

instance_id uuid
id uuid (PK)
payload json
created_at timestamp with time zone
ip_address character varying (not null)
schema_migrations

version character varying (PK)
identities

provider_id text (PK)
user_id uuid (PK)
identity_data jsonb
provider text
last_sign_in_at timestamp with time zone
created_at timestamp with time zone
updated_at timestamp with time zone
email text (generated)
id uuid (PK)
sessions

id uuid (PK)
user_id uuid
created_at timestamp with time zone
updated_at timestamp with time zone
factor_id uuid
aal aal_level
not_after timestamp with time zone
refreshed_at timestamp without time zone
user_agent text
ip inet
tag text
mfa_factors

id uuid (PK)
user_id uuid
friendly_name text
factor_type factor_type
status factor_status
created_at timestamp with time zone
updated_at timestamp with time zone
secret text
phone text
last_challenged_at timestamp with time zone
web_authn_credential jsonb
web_authn_aaguid uuid
mfa_challenges

id uuid (PK)
factor_id uuid
created_at timestamp with time zone
verified_at timestamp with time zone
ip_address inet
otp_code text
web_authn_session_data jsonb
mfa_amr_claims

session_id uuid (PK)
created_at timestamp with time zone
updated_at timestamp with time zone
authentication_method text
id uuid (PK)
sso_providers

id uuid (PK)
resource_id text
created_at timestamp with time zone
updated_at timestamp with time zone
disabled boolean
sso_domains

id uuid (PK)
sso_provider_id uuid
domain text
created_at timestamp with time zone
updated_at timestamp with time zone
saml_providers

id uuid (PK)
sso_provider_id uuid
entity_id text (unique)
metadata_xml text
metadata_url text
attribute_mapping jsonb
created_at timestamp with time zone
updated_at timestamp with time zone
name_id_format text
saml_relay_states

id uuid (PK)
sso_provider_id uuid
request_id text
for_email text
redirect_to text
created_at timestamp with time zone
updated_at timestamp with time zone
flow_state_id uuid
flow_state

id uuid (PK)
user_id uuid
auth_code text
code_challenge_method code_challenge_method
code_challenge text
provider_type text
provider_access_token text
provider_refresh_token text
created_at timestamp with time zone
updated_at timestamp with time zone
authentication_method text
auth_code_issued_at timestamp with time zone
one_time_tokens

id uuid (PK)
user_id uuid
token_type one_time_token_type
token_hash text
relates_to text
created_at timestamp without time zone
updated_at timestamp without time zone
Storage Schema
buckets

id text (PK)
name text (PK)
owner uuid
created_at timestamp with time zone
updated_at timestamp with time zone
public boolean
avif_autodetection boolean
file_size_limit bigint
allowed_mime_types text[]
owner_id text
type buckettype (default STANDARD)
objects

id uuid (PK)
bucket_id text
name text
owner uuid
created_at timestamp with time zone
updated_at timestamp with time zone
last_accessed_at timestamp with time zone
metadata jsonb
path_tokens text[] (generated)
version text
owner_id text
user_metadata jsonb
level integer
migrations

id integer (PK)
name character varying (unique)
hash character varying
executed_at timestamp without time zone
s3_multipart_uploads

id text (PK)
in_progress_size bigint
upload_signature text
bucket_id text
key text
version text
owner_id text
created_at timestamp with time zone
user_metadata jsonb
s3_multipart_uploads_parts

id uuid (PK)
upload_id text
size bigint
part_number integer
bucket_id text
key text
etag text
owner_id text
version text
created_at timestamp with time zone
prefixes

bucket_id text (PK)
name text (PK)
level integer (generated)
created_at timestamp with time zone
updated_at timestamp with time zone
buckets_analytics

id text (PK)
type buckettype (default ANALYTICS)
format text (default ICEBERG)
created_at timestamp with time zone
updated_at timestamp with time zone
Public Schema
topics

id uuid (PK)
title text
description text
icon text
is_active boolean
difficulty text
created_by uuid
created_at timestamp with time zone
updated_at timestamp with time zone
is_premium boolean
domain_id uuid
questions

id uuid (PK)
topic_id uuid
question_text text
options jsonb
correct_answer text
explanation text
difficulty text
created_by uuid
created_at timestamp with time zone
updated_at timestamp with time zone
is_premium boolean
quiz_attempts

id uuid (PK)
user_id uuid
topic_id uuid
score integer
total_questions integer
answers jsonb
time_taken integer
created_at timestamp with time zone
updated_at timestamp with time zone
payments

id uuid (PK)
user_id uuid
amount numeric
currency text (default NGN)
status text
provider text (default paystack)
provider_payment_id text
metadata jsonb
created_at timestamp with time zone
updated_at timestamp with time zone
settings

id uuid (PK)
setting_key text (unique)
setting_value text
description text
is_public boolean (default false)
created_at timestamp with time zone
updated_at timestamp with time zone
admin_users

user_id uuid (PK)
created_at timestamp with time zone
is_admin boolean (default true)
subscriptions

id uuid (PK)
user_id uuid
plan_id text
amount_paid numeric
start_date timestamp with time zone
end_date timestamp with time zone
is_active boolean (default true)
last_payment_reference text
subscription_code text
created_at timestamp with time zone
updated_at timestamp with time zone
learning_materials

id uuid (PK)
topic_id uuid
title text
content text
summary text
is_premium boolean (default false)
order_index integer (default 0)
created_at timestamp with time zone
created_by uuid
updated_at timestamp with time zone
user_quiz_results

id uuid (PK)
user_id uuid
topic_id uuid
score integer
total_questions integer
percentage numeric (generated)
time_taken integer
answers jsonb
created_at timestamp with time zone
feedback

id uuid (PK)
name text
email text
subject text
message text
user_id uuid
status text (default new)
created_at timestamp with time zone
updated_at timestamp with time zone
user_profiles

id uuid (PK)
user_id uuid (unique)
email text
full_name text
is_subscribed boolean (default false)
is_admin boolean (default false)
subscription_expires_at timestamp with time zone
subscription_status text (default free)
subscription_plan text
created_at timestamp with time zone
updated_at timestamp with time zone
domains

id uuid (PK)
name text (unique)
slug text (unique)
description text
icon text
color_theme text
difficulty_level text
estimated_duration_weeks integer (default 4)
prerequisites text[]
is_active boolean (default true)
sort_order integer (default 0)
created_at timestamp with time zone
updated_at timestamp with time zone
domain_subscription_plans

id uuid (PK)
domain_id uuid
plan_id text
name text
amount integer
interval text (default weekly)
features text[]
is_active boolean (default true)
created_at timestamp with time zone
updated_at timestamp with time zone
domain_learning_paths

id uuid (PK)
domain_id uuid
name text
description text
difficulty_level text
estimated_hours integer
sort_order integer (default 0)
is_active boolean (default true)
created_at timestamp with time zone
updated_at timestamp with time zone
path_topics

id uuid (PK)
learning_path_id uuid
topic_id uuid
sort_order integer (default 0)
is_required boolean (default true)
created_at timestamp with time zone
user_domain_progress

id uuid (PK)
user_id uuid
domain_id uuid
learning_path_id uuid
completed_topics integer (default 0)
total_topics integer (default 0)
completion_percentage numeric (default 0)
started_at timestamp with time zone
last_activity_at timestamp with time zone
completed_at timestamp with time zone
created_at timestamp with time zone
updated_at timestamp with time zone
user_entitlements (RLS disabled)

id uuid (PK)
user_id uuid
entitlement_type text
ref_id uuid
started_at timestamp with time zone
expires_at timestamp with time zone
status text (default active)
source_payment_id uuid
provider_ref text
meta jsonb (default {})
created_at timestamp with time zone
quiz_sessions

id uuid (PK)
user_id uuid
topic_id uuid
questions_data jsonb
quiz_length integer (default 10)