import React, { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Switch,
  Button,
  Stat,
  Card,
  HStack,
} from '@chakra-ui/react';
import toast from 'react-hot-toast';

interface Domain {
  id: string;
  name: string;
  is_active: boolean;
  description: string;
  domain_learning_paths: { count: number };
  domain_subscription_plans: { count: number };
}

interface DomainStats {
  total_users: number;
  completed_users: number;
  average_completion: number;
}

export const DomainManagement: React.FC = () => {
  const [domains, setDomains] = useState<Domain[]>([]);
  const [selectedDomainStats, setSelectedDomainStats] = useState<DomainStats | null>(null);

  useEffect(() => {
    fetchDomains();
  }, []);

  const fetchDomains = async () => {
    try {
      const response = await fetch('/api/admin/domains');
      const data = await response.json();
      setDomains(data);
    } catch (error) {
      toast.error('Error fetching domains');
    }
  };

  const toggleDomainStatus = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/domains/${id}/toggle`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: !currentStatus }),
      });
      
      if (response.ok) {
        setDomains(domains.map(domain =>
          domain.id === id ? { ...domain, is_active: !currentStatus } : domain
        ));
        toast.success('Domain status updated');
      }
    } catch (error) {
      toast.error('Error updating domain status');
    }
  };

  const fetchDomainStats = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/domains/${id}/stats`);
      const data = await response.json();
      setSelectedDomainStats(data);
    } catch (error) {
      toast.error('Error fetching domain stats');
    }
  };

  return (
    <Box p={4}>
      <Table.Root variant="outline">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader>Name</Table.ColumnHeader>
            <Table.ColumnHeader>Status</Table.ColumnHeader>
            <Table.ColumnHeader>Learning Paths</Table.ColumnHeader>
            <Table.ColumnHeader>Subscription Plans</Table.ColumnHeader>
            <Table.ColumnHeader>Actions</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {domains.map((domain) => (
            <Table.Row key={domain.id}>
              <Table.Cell>{domain.name}</Table.Cell>
              <Table.Cell>
                <Switch
                  checked={domain.is_active}
                  onCheckedChange={() => toggleDomainStatus(domain.id, domain.is_active)}
                />
              </Table.Cell>
              <Table.Cell>{domain.domain_learning_paths.count}</Table.Cell>
              <Table.Cell>{domain.domain_subscription_plans.count}</Table.Cell>
              <Table.Cell>
                <Button
                  size="sm"
                  onClick={() => fetchDomainStats(domain.id)}
                >
                  View Stats
                </Button>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>

      {selectedDomainStats && (
        <Card.Root mt={4}>
          <Card.Body>
            <HStack gap={6}>
              <Stat.Root>
                <Stat.Label>Total Users</Stat.Label>
                <Stat.ValueText>{selectedDomainStats.total_users}</Stat.ValueText>
              </Stat.Root>
              <Stat.Root>
                <Stat.Label>Completed Users</Stat.Label>
                <Stat.ValueText>{selectedDomainStats.completed_users}</Stat.ValueText>
              </Stat.Root>
              <Stat.Root>
                <Stat.Label>Average Completion</Stat.Label>
                <Stat.ValueText>{selectedDomainStats.average_completion.toFixed(2)}%</Stat.ValueText>
              </Stat.Root>
            </HStack>
          </Card.Body>
        </Card.Root>
      )}
    </Box>
  );
};