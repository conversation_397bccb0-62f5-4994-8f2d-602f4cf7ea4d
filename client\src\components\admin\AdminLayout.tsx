import React from 'react';
import { Box, Flex, Icon, Text } from '@chakra-ui/react';
import { FaFolder } from 'react-icons/fa';
import { Link, Routes, Route } from 'react-router-dom';
import { DomainManagement } from './DomainManagement';

const AdminLayout = () => {
  const navItems = [
    {
      name: 'Domain Management',
      path: '/domains',
      icon: <Icon as={FaFolder} />,
      component: DomainManagement
    }
  ];

  return (
    <Flex>
      <Box as="nav" w="250px" bg="gray.100" p={4}>
        {navItems.map((item) => (
          <Link 
            key={item.path}
            to={`/admin${item.path}`}
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px',
              textDecoration: 'none'
            }}
          >
            {item.icon}
            <Text ml={2}>{item.name}</Text>
          </Link>
        ))}
      </Box>

      <Box flex="1" p={4}>
        <Routes>
          {navItems.map((item) => (
            <Route 
              key={item.path}
              path={item.path}
              element={<item.component />}
            />
          ))}
        </Routes>
      </Box>
    </Flex>
  );
};

export default AdminLayout;