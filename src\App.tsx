
import { Toaster } from "@/components/ui/toaster";
import { Analytics } from "@vercel/analytics/react";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "./hooks/use-auth";
import { usePageStatePersistence } from "./hooks/use-page-state-persistence";
import Index from "./pages/Index";
import QuizPage from "./pages/QuizPage";
import QuizSetupPage from "./pages/QuizSetupPage";
import QuizzesPage from "./pages/QuizzesPage";
import ProfilePage from "./pages/ProfilePage";
import AdminDashboard from "./pages/AdminDashboard";
import AuthPage from "./pages/AuthPage";
import DomainsPage from "./pages/DomainsPage";
import DomainDetailPage from "./pages/DomainDetailPage";
import DomainLearningPage from "./pages/DomainLearningPage";
import DomainQuizzesPage from "./pages/DomainQuizzesPage";
import VerifyEmailPage from "./pages/VerifyEmailPage";
import NotFound from "./pages/NotFound";
import AboutPage from "./pages/AboutPage";
import ContactPage from "./pages/ContactPage";
import PaymentSuccess from "./pages/PaymentSuccess";
import PaymentTroubleshoot from "./pages/PaymentTroubleshoot";
import LearningMaterialsPage from "./pages/LearningMaterialsPage";
import LearningMaterialPage from "./pages/LearningMaterialPage";
import { isUserAdmin } from "./utils/auth-helpers";
import { useEffect, useState } from "react";
import { appConfig, featureFlags } from "./config";
import InstallPWA from "./components/InstallPWA";
import ScrollToTop from "./components/ScrollToTop";
import DeveloperTools from "./pages/DeveloperTools";
import ImportQuestionsPage from "./pages/ImportQuestionsPage";
import AdminDebug from "./components/AdminDebug";
import { initializeEmailServices, logEmailConfigurationStatus } from "./utils/email-init";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Loader2, Plus, Edit, Trash2, Users, BookOpen } from "lucide-react";
import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";

interface Domain {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  is_premium: boolean;
  created_at: string;
}

const queryClient = new QueryClient();

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();

  if (isLoading) return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  if (!user) return <Navigate to="/auth" replace />;

  return <>{children}</>;
};

// Admin route component - now with proper admin check
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isCheckingAdmin, setIsCheckingAdmin] = useState(true);

  useEffect(() => {
    async function checkAdminStatus() {
      if (user) {
        setIsCheckingAdmin(true);
        const adminStatus = await isUserAdmin(user);
        setIsAdmin(adminStatus);
        setIsCheckingAdmin(false);
      } else {
        setIsAdmin(false);
        setIsCheckingAdmin(false);
      }
    }

    checkAdminStatus();
  }, [user]);

  if (isLoading || isCheckingAdmin) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  if (!user) return <Navigate to="/auth" replace />;

  // Check if user is an admin
  if (!isAdmin) return <Navigate to="/" replace />;

  return <>{children}</>;
};

const App = () => {
  // Set document title from environment variables
  useEffect(() => {
    document.title = appConfig.name;

    // Initialize email services
    initializeEmailServices();
    
    // Log email configuration status
    logEmailConfigurationStatus();

    // Log debug information if debug mode is enabled
    if (featureFlags.enableDebugMode) {
      console.log('App config:', appConfig);
      console.log('Feature flags:', featureFlags);
    }
  }, []);

  // Component to handle page state persistence
  const AppWithPersistence = () => {
    usePageStatePersistence();

    return (
      <>
        <ScrollToTop />
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/auth" element={<AuthPage />} />
          <Route path="/auth/verify" element={<VerifyEmailPage />} />
          <Route path="/verify" element={<VerifyEmailPage />} />
          <Route path="/domains" element={<DomainsPage />} />
          <Route path="/domains/:domainSlug" element={<DomainDetailPage />} />
          <Route path="/domains/:domainSlug/learn" element={<DomainLearningPage />} />
          <Route path="/domains/:domainSlug/quizzes" element={<DomainQuizzesPage />} />
          <Route path="/quizzes" element={<QuizzesPage />} />
          <Route path="/quiz/:topicId/setup" element={<QuizSetupPage />} />
          <Route path="/quiz/:topicId" element={<QuizPage />} />
          <Route path="/learn" element={<LearningMaterialsPage />} />
          <Route path="/learn/:materialId" element={<LearningMaterialPage />} />
          <Route path="/profile" element={<ProtectedRoute><ProfilePage /></ProtectedRoute>} />
          <Route path="/admin" element={<AdminRoute><AdminDashboard /></AdminRoute>} />
          <Route path="/admin/import" element={<AdminRoute><ImportQuestionsPage /></AdminRoute>} />
          <Route path="/developer" element={<AdminRoute><DeveloperTools /></AdminRoute>} />
          <Route path="/admin-debug" element={<AdminDebug />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/payment/success" element={<PaymentSuccess />} />
          <Route path="/payment/troubleshoot" element={<PaymentTroubleshoot />} />
          <Route path="/admin/domains" element={<AdminDomainsPage />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
        <InstallPWA />
      </>
    );
  };

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <AuthProvider>
            <AppWithPersistence />
          </AuthProvider>
        </BrowserRouter>
        <Analytics />
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
