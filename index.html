<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="/secquiz-logo.svg" type="image/svg+xml" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#4f46e5" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="SecQuiz" />
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />

    <!-- Security headers -->
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://va.vercel-scripts.com https://vitals.vercel-insights.com;
      script-src-elem 'self' 'unsafe-inline' https://va.vercel-scripts.com https://vitals.vercel-insights.com;
      connect-src 'self' https://va.vercel-scripts.com https://vitals.vercel-insights.com https://*.supabase.co wss://*.supabase.co https://api.paystack.co;
      img-src 'self' data: https:;
      style-src 'self' 'unsafe-inline';
      font-src 'self' data:;
    ">
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <!-- X-Frame-Options removed - should only be set via HTTP headers -->
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <title>SecQuiz</title>
    <meta name="description" content="A cybersecurity education platform with interactive quizzes" />
    <meta name="author" content="Gigsdev - Olajide Igbalaye " />

    <meta property="og:title" content="SecQuiz" />
    <meta property="og:description" content="A cybersecurity education platform with interactive quizzes" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/secquiz-social.svg" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@secquiz" />
    <meta name="twitter:image" content="/secquiz-social.svg" />
  </head>

  <body>
    <div id="root"></div>
    <!-- SecQuiz - A cybersecurity education platform -->
    <script type="module" src="/src/main.tsx"></script>
    <script src="/register-sw.js"></script>
  </body>
</html>
